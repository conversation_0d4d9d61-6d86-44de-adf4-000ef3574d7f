<?php
/**
 * PHP版本的JavaScript签名算法还原
 * 对应JavaScript代码：pK[xm] = function(r, t) { ... }
 */

function signData($data, $key) {
    // 检查数据类型，如果不是数组/对象则直接返回JSON
    if (!is_array($data) && !is_object($data)) {
        return json_encode($data, JSON_UNESCAPED_UNICODE);
    }
    
    // 将对象转换为数组便于处理
    $dataArray = is_object($data) ? (array)$data : $data;
    
    // 拼接字符串
    $concatenatedString = "";
    
    // 遍历对象的所有属性
    foreach ($dataArray as $key_name => $value) {
        $valueType = gettype($value);
        
        // 根据JavaScript逻辑，排除某些类型的值
        // pr != s && yr != s && VA != s || (n += o)
        // 这里我们包含：string, integer, double(float), boolean
        // 排除：array, object, NULL, resource, unknown type
        if ($valueType === 'string' || $valueType === 'integer' || 
            $valueType === 'double' || $valueType === 'boolean') {
            
            // 布尔值转换为字符串
            if ($valueType === 'boolean') {
                $concatenatedString .= $value ? 'true' : 'false';
            } else {
                $concatenatedString .= (string)$value;
            }
        }
    }
    
    // 添加当前时间戳（毫秒）
    $timestamp = round(microtime(true) * 1000);
    $concatenatedString .= $timestamp;
    var_dump($concatenatedString);
    exit;
    
    // 将时间戳保存到数据中
    $dataArray['_time_'] = $timestamp;
    
    // 生成HMAC签名
    // 使用SHA256算法（常见的HMAC算法）
    $signature = hash_hmac('sha256', $concatenatedString, $key);
    
    // 将签名保存到数据的-1属性
    $dataArray['-1'] = $signature;
    
    // 返回JSON字符串
    return json_encode($dataArray, JSON_UNESCAPED_UNICODE);
}

// 主程序
function main() {
    // 读取r.json文件
    $jsonFile = __DIR__ . '/r.json';
    if (!file_exists($jsonFile)) {
        die("文件 {$jsonFile} 不存在\n");
    }
    
    $jsonContent = file_get_contents($jsonFile);
    $data = json_decode($jsonContent, true);
    
    if ($data === null) {
        die("JSON解析失败: " . json_last_error_msg() . "\n");
    }
    
    // 密钥
    $key = "U3161217324418554";
    
    echo "原始数据键数量: " . count($data) . "\n";
    echo "开始签名处理...\n";
    
    // 执行签名
    $signedData = signData($data, $key);
    
    echo "签名完成！\n";
    
    // 解析签名后的数据以显示结果
    $signedArray = json_decode($signedData, true);
    
    echo "时间戳: " . $signedArray['_time_'] . "\n";
    echo "签名: " . $signedArray['-1'] . "\n";
    
    // 保存签名后的数据到文件
    $outputFile = __DIR__ . '/signed_data.json';
    file_put_contents($outputFile, $signedData);
    echo "签名后的数据已保存到: {$outputFile}\n";
    
    return $signedData;
}

// 验证签名的函数
function verifySignature($data, $key) {
    if (!is_array($data)) {
        $data = json_decode($data, true);
    }
    
    if (!isset($data['-1']) || !isset($data['_time_'])) {
        return false;
    }
    
    $originalSignature = $data['-1'];
    $timestamp = $data['_time_'];
    
    // 移除签名和时间戳
    unset($data['-1']);
    unset($data['_time_']);
    
    // 重新计算签名
    $concatenatedString = "";
    foreach ($data as $value) {
        $valueType = gettype($value);
        if ($valueType === 'string' || $valueType === 'integer' || 
            $valueType === 'double' || $valueType === 'boolean') {
            
            if ($valueType === 'boolean') {
                $concatenatedString .= $value ? 'true' : 'false';
            } else {
                $concatenatedString .= (string)$value;
            }
        }
    }
    
    $concatenatedString .= $timestamp;
    $calculatedSignature = hash_hmac('sha256', $concatenatedString, $key);
    
    return $originalSignature === $calculatedSignature;
}

// 如果直接运行此脚本
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    $result = main();
    
    // 验证签名
    echo "\n验证签名...\n";
    $isValid = verifySignature($result, "U3161217324418554");
    echo "签名验证结果: " . ($isValid ? "有效" : "无效") . "\n";
}
?>
